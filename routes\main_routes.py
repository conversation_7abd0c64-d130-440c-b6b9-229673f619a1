#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
主要路由模块
包含首页、设备管理和OTA相关的路由
"""

import os
import time
import json
from datetime import datetime
from threading import Thread
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename

from models.device import Device
from models.ota_task import OtaTask
from models.firmware import Firmware
from models.database import db
from services.iot_client_manager import IoTClientManager
from services.ota_service import OtaTaskThreadManager
from utils.logger import LoggerManager
from utils.socket_manager import emit_task_update

# 获取日志记录器
logger = LoggerManager.get_logger()

# 创建蓝图
main_bp = Blueprint('main', __name__)

# 获取OTA任务管理器实例
ota_task_manager = OtaTaskThreadManager()

@main_bp.route('/')
@login_required
def index():
    """首页"""
    devices = Device.query.all()
    firmwares = Firmware.query.order_by(Firmware.upload_time.desc()).all()

    # 从应用上下文获取设备状态缓存
    device_status_cache = current_app.config.get('DEVICE_STATUS_CACHE', {})
    device_status_lock = current_app.config.get('DEVICE_STATUS_LOCK')

    with device_status_lock:
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        # 确保所有设备都有状态记录
        for device in devices:
            if device.id not in device_status_cache:
                device_status_cache[device.id] = {
                    'is_online': False,
                    'last_check': current_time,
                    'last_online_time': '未知'
                }

    return render_template('index.html', devices=devices, firmwares=firmwares, device_status_cache=device_status_cache)

@main_bp.route('/ota/tasks')
@login_required
def ota_tasks():
    """OTA任务列表页面"""
    # 只获取固件列表，任务列表通过Ajax加载
    firmwares = Firmware.query.order_by(Firmware.upload_time.desc()).all()

    # 获取任务统计信息
    total_tasks = OtaTask.query.count()
    success_count = OtaTask.query.filter_by(status='成功').count()
    failed_count = OtaTask.query.filter_by(status='失败').count()
    in_progress_count = OtaTask.query.filter_by(status='进行中').count()
    waiting_count = OtaTask.query.filter_by(status='等待中').count()

    # 传递空的任务列表和统计信息
    return render_template('ota_tasks.html',
                         tasks=[],
                         firmwares=firmwares,
                         stats={
                             'total': total_tasks,
                             'success': success_count,
                             'failed': failed_count,
                             'in_progress': in_progress_count,
                             'waiting': waiting_count
                         })

@main_bp.route('/api/ota/tasks')
@login_required
def get_ota_tasks_api():
    """获取OTA任务列表API（支持分页、搜索、筛选）"""
    try:
        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        # 获取搜索和筛选参数
        search = request.args.get('search', '').strip()
        status_filter = request.args.get('status', 'all')
        device_id_filter = request.args.get('device_id', '').strip()
        firmware_filter = request.args.get('firmware', '').strip()
        date_filter = request.args.get('date', '').strip()

        # 构建查询
        query = OtaTask.query.join(Device)

        # 搜索条件（任务ID或设备ID）
        if search:
            query = query.filter(
                db.or_(
                    OtaTask.id.like(f'%{search}%'),
                    Device.device_id.contains(search)
                )
            )

        # 状态筛选
        if status_filter and status_filter != 'all':
            query = query.filter(OtaTask.status == status_filter)

        # 设备ID筛选
        if device_id_filter:
            query = query.filter(Device.device_id.contains(device_id_filter))

        # 固件版本筛选
        if firmware_filter:
            query = query.filter(OtaTask.firmware_version.contains(firmware_filter))

        # 日期筛选
        if date_filter:
            try:
                from datetime import datetime
                from utils.database_utils import date_filter as db_date_filter
                filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
                query = query.filter(db_date_filter(OtaTask.created_at, filter_date))
            except ValueError:
                pass  # 忽略无效的日期格式

        # 按创建时间降序排列
        query = query.order_by(OtaTask.created_at.desc())

        # 执行分页查询
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # 构建返回数据
        tasks_data = []
        for task in pagination.items:
            tasks_data.append({
                'id': task.id,
                'device_id': task.device.device_id,
                'device_name': task.device.device_id,
                'firmware_version': task.firmware_version,
                'status': task.status,
                'progress': task.progress,
                'error_message': task.error_message or '',
                'created_at': task.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'updated_at': task.updated_at.strftime('%Y-%m-%d %H:%M:%S')
            })

        return jsonify({
            'success': True,
            'tasks': tasks_data,
            'pagination': {
                'page': pagination.page,
                'pages': pagination.pages,
                'per_page': pagination.per_page,
                'total': pagination.total,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev,
                'next_num': pagination.next_num,
                'prev_num': pagination.prev_num
            }
        })

    except Exception as e:
        logger.error(f"获取OTA任务列表失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@main_bp.route('/ota/start', methods=['POST'])
@login_required
def start_ota():
    """开始OTA升级"""
    from services.ota_service import start_ota_task
    
    try:
        # 获取请求数据
        if request.is_json:
            data = request.get_json()
            device_ids = data.get('device_ids', [])
            firmware_id = data.get('firmware_id')

            if not device_ids or not firmware_id:
                return jsonify({'success': False, 'message': '缺少必要参数'})

            # 调用服务层函数处理OTA任务创建
            success, message = start_ota_task(device_ids, firmware_id=firmware_id)

        else:
            # 兼容旧的上传文件方式
            device_ids = request.form.getlist('device_ids[]')
            firmware_file = request.files.get('firmware_file')

            if not device_ids or not firmware_file:
                return jsonify({'success': False, 'message': '缺少必要参数'})

            # 调用服务层函数处理OTA任务创建
            success, message = start_ota_task(device_ids, firmware_file=firmware_file)

        return jsonify({'success': success, 'message': message})

    except Exception as e:
        logger.error(f"创建OTA任务失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@main_bp.route('/ota/task/delete/<int:id>')
@login_required
def delete_task(id):
    """删除OTA任务（兼容旧版本）"""
    from models.database import db

    task = OtaTask.query.get_or_404(id)

    try:
        # 如果任务处于进行中，则返回进行中的任务不允许删除
        if task.status == "进行中":
            flash('进行中的任务删除后仍然会继续执行！', 'danger')

        # 删除任务记录
        db.session.delete(task)
        db.session.commit()

        flash('任务删除成功', 'success')
    except Exception as e:
        db.session.rollback()
        logger.error(f"删除任务失败: {e}")
        flash('删除任务失败', 'danger')

    return redirect(url_for('main.ota_tasks'))

@main_bp.route('/api/ota/task/delete/<int:id>', methods=['DELETE'])
@login_required
def delete_task_api(id):
    """删除OTA任务API"""
    from models.database import db

    try:
        task = OtaTask.query.get_or_404(id)

        # 如果任务处于进行中，给出警告但允许删除
        warning_message = None
        if task.status == "进行中":
            warning_message = "进行中的任务删除后仍然会继续执行！"

        # 删除任务记录
        db.session.delete(task)
        db.session.commit()

        response_data = {
            'success': True,
            'message': '任务删除成功'
        }

        if warning_message:
            response_data['warning'] = warning_message

        return jsonify(response_data)

    except Exception as e:
        db.session.rollback()
        logger.error(f"删除任务失败: {e}")
        return jsonify({'success': False, 'message': f'删除任务失败: {str(e)}'}), 500

@main_bp.route('/api/ota/tasks/stats')
@login_required
def get_ota_tasks_stats():
    """获取OTA任务统计信息API"""
    try:
        total_tasks = OtaTask.query.count()
        success_count = OtaTask.query.filter_by(status='成功').count()
        failed_count = OtaTask.query.filter_by(status='失败').count()
        in_progress_count = OtaTask.query.filter_by(status='进行中').count()
        waiting_count = OtaTask.query.filter_by(status='等待中').count()

        return jsonify({
            'success': True,
            'stats': {
                'total': total_tasks,
                'success': success_count,
                'failed': failed_count,
                'in_progress': in_progress_count,
                'waiting': waiting_count
            }
        })

    except Exception as e:
        logger.error(f"获取OTA任务统计失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@main_bp.route('/ota/task/<int:task_id>/retry', methods=['POST'])
@login_required
def retry_task(task_id):
    """重试OTA任务"""
    from services.ota_service import retry_ota_task
    
    try:
        success, message = retry_ota_task(task_id)
        return jsonify({'success': success, 'message': message})
    except Exception as e:
        logger.error(f"重试任务失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@main_bp.route('/test_websocket')
@login_required
def test_websocket():
    """测试WebSocket连接"""
    try:
        with current_app.app_context():
            emit_task_update(999, "测试", 50, "这是一个测试消息")
            logger.info("测试消息已发送")
        return jsonify({'success': True, 'message': '测试消息已发送'})
    except Exception as e:
        logger.error(f"发送测试消息失败: {e}")
        return jsonify({'success': False, 'error': str(e)})
